"""
Clinical Metabolomics Oracle (CMO) FastAPI Application

This is the main FastAPI application for the Clinical Metabolomics Oracle,
a sophisticated AI-powered system for clinical metabolomics research.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# Import configuration
from app.core.config import settings

# Import API routers
from app.api.auth import router as auth_router
from app.api.users import router as users_router
from app.api.chat import router as chat_router

# Create FastAPI application instance
app = FastAPI(
    title="Clinical Metabolomics Oracle API",
    description="Advanced AI-powered system for clinical metabolomics research and analysis",
    version="1.0.0",
    docs_url="/docs" if settings.ENVIRONMENT == "development" else None,
    redoc_url="/redoc" if settings.ENVIRONMENT == "development" else None,
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers with /api/v1 prefix
app.include_router(auth_router, prefix="/api/v1")
app.include_router(users_router, prefix="/api/v1")
app.include_router(chat_router, prefix="/api/v1")

@app.get("/")
async def root():
    """Root endpoint for health check"""
    return {
        "message": "Clinical Metabolomics Oracle API",
        "version": "1.0.0",
        "status": "operational"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}
