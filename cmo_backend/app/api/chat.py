"""
Chat API endpoints

This module provides chat-related endpoints including chat history
retrieval, thread management, and streaming chat functionality.
"""

from typing import List, AsyncGenerator
import json
import asyncio
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from openai import Async<PERSON>penA<PERSON>
from openai.types.beta.threads.runs import RunStep

from app.core.database import get_db
from app.core.dependencies import get_current_user, get_optional_current_user
from app.core.config import settings
from app.crud.chat import (
    get_user_chat_threads,
    get_chat_thread_by_id,
    get_thread_messages,
    create_chat_thread,
    create_chat_message
)
from app.models.user import User
from app.models.chat import (
    ChatThreadPublic,
    ChatMessagePublic,
    ChatRequest,
    ChatThreadCreate,
    ChatMessageCreate,
    SenderType
)


router = APIRouter(prefix="/chat", tags=["chat"])

# Initialize OpenAI client
openai_client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)


async def generate_chat_stream(
    user_message: str,
    thread_id: int,
    openai_thread_id: str,
    current_user: User,
    db: AsyncSession
) -> AsyncGenerator[str, None]:
    """
    Generate streaming chat response from OpenAI Assistant

    Args:
        user_message: User's message
        thread_id: Database thread ID
        openai_thread_id: OpenAI thread ID
        current_user: Current user
        db: Database session

    Yields:
        Server-Sent Events formatted strings
    """
    try:
        # Add user message to OpenAI thread
        await openai_client.beta.threads.messages.create(
            thread_id=openai_thread_id,
            role="user",
            content=user_message
        )

        # Save user message to database
        user_msg_data = ChatMessageCreate(
            content=user_message,
            sender=SenderType.USER
        )
        await create_chat_message(db, thread_id, user_msg_data)

        # Start streaming run with OpenAI Assistant
        ai_response_content = ""

        async with openai_client.beta.threads.runs.stream(
            thread_id=openai_thread_id,
            assistant_id=settings.CMO_ASSISTANT_ID,
        ) as stream:
            async for event in stream:
                # Handle text delta events for streaming
                if hasattr(event, 'event') and event.event == "thread.message.delta":
                    if hasattr(event, 'data') and hasattr(event.data, 'delta'):
                        if hasattr(event.data.delta, 'content'):
                            for content_block in event.data.delta.content:
                                if hasattr(content_block, 'text') and hasattr(content_block.text, 'value'):
                                    chunk = content_block.text.value
                                    ai_response_content += chunk
                                    # Yield chunk in SSE format
                                    yield f"data: {json.dumps({'type': 'token', 'content': chunk})}\n\n"

        # Save AI response to database
        if ai_response_content:
            ai_msg_data = ChatMessageCreate(
                content=ai_response_content,
                sender=SenderType.AI
            )
            await create_chat_message(db, thread_id, ai_msg_data)

        # Send completion event
        yield f"data: {json.dumps({'type': 'complete'})}\n\n"

    except Exception as e:
        # Send error event
        error_msg = f"Error generating response: {str(e)}"
        yield f"data: {json.dumps({'type': 'error', 'message': error_msg})}\n\n"


@router.post("/")
async def stream_chat(
    chat_request: ChatRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Stream chat response from OpenAI Assistant

    Args:
        chat_request: Chat request containing user message and optional thread_id
        current_user: Current authenticated user
        db: Database session

    Returns:
        StreamingResponse with Server-Sent Events

    Raises:
        HTTPException: If OpenAI configuration is missing or thread not found
    """
    # Validate OpenAI configuration
    if not settings.OPENAI_API_KEY or not settings.CMO_ASSISTANT_ID:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="OpenAI configuration is missing. Please check OPENAI_API_KEY and CMO_ASSISTANT_ID."
        )

    # Handle existing thread or create new one
    if chat_request.thread_id:
        # Get existing thread
        thread = await get_chat_thread_by_id(db, chat_request.thread_id, current_user.id)
        if not thread:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Chat thread not found"
            )

        # For now, create a new OpenAI thread for each request
        # In a production system, you'd want to store and reuse OpenAI thread IDs
        openai_thread = await openai_client.beta.threads.create()
        openai_thread_id = openai_thread.id

    else:
        # Create new thread in database
        thread_data = ChatThreadCreate(title=f"Chat - {chat_request.message[:50]}...")
        thread = await create_chat_thread(db, current_user.id, thread_data)

        # Create new OpenAI thread
        openai_thread = await openai_client.beta.threads.create()
        openai_thread_id = openai_thread.id

    # Return streaming response
    return StreamingResponse(
        generate_chat_stream(
            chat_request.message,
            thread.id,
            openai_thread_id,
            current_user,
            db
        ),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )


async def generate_guest_chat_stream(
    user_message: str,
    openai_thread_id: str
) -> AsyncGenerator[str, None]:
    """
    Generate streaming chat response from OpenAI Assistant for guest users

    Args:
        user_message: User's message
        openai_thread_id: OpenAI thread ID

    Yields:
        Server-sent events with chat response chunks
    """
    try:
        # Add user message to OpenAI thread
        await openai_client.beta.threads.messages.create(
            thread_id=openai_thread_id,
            role="user",
            content=user_message
        )

        yield f"data: {json.dumps({'type': 'status', 'message': 'Processing your message...'})}\n\n"

        # Create and run assistant
        run = await openai_client.beta.threads.runs.create(
            thread_id=openai_thread_id,
            assistant_id=settings.CMO_ASSISTANT_ID,
            stream=True
        )

        # Stream the response
        accumulated_text = ""
        async for event in run:
            if hasattr(event, 'event') and event.event == 'thread.message.delta':
                if hasattr(event, 'data') and hasattr(event.data, 'delta'):
                    if hasattr(event.data.delta, 'content'):
                        for content in event.data.delta.content:
                            if hasattr(content, 'text') and hasattr(content.text, 'value'):
                                chunk = content.text.value
                                accumulated_text += chunk

                                # Send the new chunk only (not accumulated text)
                                yield f"data: {json.dumps({'type': 'token', 'content': chunk})}\n\n"
                                await asyncio.sleep(0.01)  # Small delay for better UX

            elif hasattr(event, 'event') and event.event == 'thread.run.completed':
                yield f"data: {json.dumps({'type': 'complete'})}\n\n"
                break

            elif hasattr(event, 'event') and event.event == 'thread.run.failed':
                yield f"data: {json.dumps({'type': 'error', 'message': 'Assistant run failed'})}\n\n"
                break

    except Exception as e:
        yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"


@router.post("/guest")
async def stream_guest_chat(chat_request: ChatRequest):
    """
    Stream chat response from OpenAI Assistant for guest users

    This endpoint allows unauthenticated users to chat with the assistant
    without saving conversation history.

    Args:
        chat_request: Chat request containing user message

    Returns:
        StreamingResponse with Server-Sent Events

    Raises:
        HTTPException: If OpenAI configuration is missing
    """
    # Validate OpenAI configuration
    if not settings.OPENAI_API_KEY or not settings.CMO_ASSISTANT_ID:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="OpenAI configuration is missing. Please check OPENAI_API_KEY and CMO_ASSISTANT_ID."
        )

    # Create new OpenAI thread for guest session
    openai_thread = await openai_client.beta.threads.create()
    openai_thread_id = openai_thread.id

    # Return streaming response
    return StreamingResponse(
        generate_guest_chat_stream(
            chat_request.message,
            openai_thread_id
        ),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )


@router.get("/history", response_model=List[ChatThreadPublic])
async def get_chat_history(
    skip: int = Query(0, ge=0, description="Number of threads to skip"),
    limit: int = Query(100, ge=1, le=100, description="Maximum number of threads to return"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all chat threads for the current user
    
    Args:
        skip: Number of threads to skip for pagination
        limit: Maximum number of threads to return
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List of chat threads (without messages)
    """
    threads = await get_user_chat_threads(db, current_user.id, skip, limit)
    
    return [
        ChatThreadPublic(
            id=thread.id,
            title=thread.title,
            created_at=thread.created_at,
            updated_at=thread.updated_at
        )
        for thread in threads
    ]


@router.get("/history/{thread_id}", response_model=ChatThreadPublic)
async def get_chat_thread_with_messages(
    thread_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific chat thread with all its messages
    
    Args:
        thread_id: Thread ID to retrieve
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Chat thread with all messages
        
    Raises:
        HTTPException: If thread not found or not owned by user
    """
    thread = await get_chat_thread_by_id(db, thread_id, current_user.id)
    
    if not thread:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat thread not found"
        )
    
    # Convert messages to public format
    messages = [
        ChatMessagePublic(
            id=message.id,
            content=message.content,
            sender=message.sender,
            timestamp=message.timestamp
        )
        for message in thread.messages
    ]
    
    return ChatThreadPublic(
        id=thread.id,
        title=thread.title,
        created_at=thread.created_at,
        updated_at=thread.updated_at,
        messages=messages
    )
