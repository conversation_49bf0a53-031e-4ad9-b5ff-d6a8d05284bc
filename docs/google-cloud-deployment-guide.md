# Clinical Metabolomics Oracle - Google Cloud Deployment Guide

## Complete Step-by-Step Installation on Ubuntu 24.04 Server

This comprehensive guide will walk you through deploying the Clinical Metabolomics Oracle chatbot website on a Google Cloud Platform (GCP) Ubuntu 24.04 server.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Google Cloud Setup](#google-cloud-setup)
3. [Server Initial Setup](#server-initial-setup)
4. [Install Dependencies](#install-dependencies)
5. [Database Setup](#database-setup)
6. [Application Deployment](#application-deployment)
7. [Web Server Configuration](#web-server-configuration)
8. [SSL Certificate Setup](#ssl-certificate-setup)
9. [Environment Configuration](#environment-configuration)
10. [Service Management](#service-management)
11. [Monitoring and Logging](#monitoring-and-logging)
12. [Backup Strategy](#backup-strategy)
13. [Troubleshooting](#troubleshooting)

## Prerequisites

Before starting, ensure you have:

- Google Cloud Platform account with billing enabled
- Domain name (optional but recommended)
- Basic knowledge of Linux command line
- SSH client installed on your local machine

## Google Cloud Setup

### Step 1: Create a New Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Select a project" → "New Project"
3. Enter project name: `clinical-metabolomics-oracle`
4. Click "Create"

### Step 2: Enable Required APIs

```bash
# Enable Compute Engine API
gcloud services enable compute.googleapis.com

# Enable Cloud SQL API (if using managed database)
gcloud services enable sqladmin.googleapis.com

# Enable Cloud Storage API (for file storage)
gcloud services enable storage.googleapis.com
```

### Step 3: Create VM Instance

1. Navigate to Compute Engine → VM instances
2. Click "Create Instance"
3. Configure the instance:

**Basic Configuration:**
- Name: `cmo-production-server`
- Region: `us-central1` (or closest to your users)
- Zone: `us-central1-a`

**Machine Configuration:**
- Machine family: `General-purpose`
- Series: `E2`
- Machine type: `e2-standard-2` (2 vCPU, 8 GB memory)

**Boot Disk:**
- Operating System: `Ubuntu`
- Version: `Ubuntu 24.04 LTS`
- Boot disk type: `SSD persistent disk`
- Size: `20 GB` (minimum)

**Firewall:**
- ✅ Allow HTTP traffic
- ✅ Allow HTTPS traffic

4. Click "Create"

### Step 4: Configure Firewall Rules

```bash
# Allow HTTP traffic
gcloud compute firewall-rules create allow-http \
    --allow tcp:80 \
    --source-ranges 0.0.0.0/0 \
    --description "Allow HTTP traffic"

# Allow HTTPS traffic
gcloud compute firewall-rules create allow-https \
    --allow tcp:443 \
    --source-ranges 0.0.0.0/0 \
    --description "Allow HTTPS traffic"

# Allow custom application port (if needed)
gcloud compute firewall-rules create allow-app-port \
    --allow tcp:8000 \
    --source-ranges 0.0.0.0/0 \
    --description "Allow application port"
```

### Step 5: Reserve Static IP Address

```bash
# Reserve external IP address
gcloud compute addresses create cmo-static-ip \
    --region=us-central1

# Get the reserved IP address
gcloud compute addresses describe cmo-static-ip \
    --region=us-central1 \
    --format="get(address)"
```

### Step 6: Assign Static IP to VM

1. Go to Compute Engine → VM instances
2. Click on your instance name
3. Click "Edit"
4. Under "Network interfaces" → "External IP" → Select your reserved IP
5. Click "Done" → "Save"

## Server Initial Setup

### Step 1: Connect to Your Server

```bash
# Connect via SSH from Google Cloud Console or use gcloud CLI
gcloud compute ssh cmo-production-server --zone=us-central1-a

# Or use SSH with external IP
ssh username@YOUR_EXTERNAL_IP
```

### Step 2: Update System Packages

```bash
# Update package list
sudo apt update

# Upgrade all packages
sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git unzip software-properties-common \
    apt-transport-https ca-certificates gnupg lsb-release
```

### Step 3: Create Application User

```bash
# Create dedicated user for the application
sudo adduser --system --group --home /opt/cmo cmo

# Add user to sudo group (if needed for maintenance)
sudo usermod -aG sudo cmo

# Switch to application user
sudo su - cmo
```

### Step 4: Configure SSH Keys (Optional)

```bash
# Generate SSH key for the cmo user
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# Add your public key to authorized_keys
mkdir -p ~/.ssh
echo "YOUR_PUBLIC_KEY" >> ~/.ssh/authorized_keys
chmod 700 ~/.ssh
chmod 600 ~/.ssh/authorized_keys
```

## Install Dependencies

### Step 1: Install Node.js and npm

```bash
# Install Node.js 20.x LTS
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs

# Verify installation
node --version  # Should show v20.x.x
npm --version   # Should show 10.x.x

# Install global packages
sudo npm install -g pm2 yarn
```

### Step 2: Install Python and pip

```bash
# Install Python 3.12 and pip
sudo apt install -y python3 python3-pip python3-venv python3-dev

# Install build essentials for Python packages
sudo apt install -y build-essential libssl-dev libffi-dev

# Verify installation
python3 --version  # Should show Python 3.12.x
pip3 --version
```

### Step 3: Install PostgreSQL

```bash
# Install PostgreSQL
sudo apt install -y postgresql postgresql-contrib postgresql-client

# Start and enable PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Verify installation
sudo systemctl status postgresql
```

### Step 4: Install Redis

```bash
# Install Redis
sudo apt install -y redis-server

# Configure Redis
sudo nano /etc/redis/redis.conf

# Find and modify these lines:
# supervised systemd
# maxmemory 256mb
# maxmemory-policy allkeys-lru

# Start and enable Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server

# Verify installation
redis-cli ping  # Should return PONG
```

### Step 5: Install Nginx

```bash
# Install Nginx
sudo apt install -y nginx

# Start and enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# Verify installation
sudo systemctl status nginx
```

## Database Setup

### Step 1: Configure PostgreSQL

```bash
# Switch to postgres user
sudo su - postgres

# Create database and user
createdb clinical_metabolomics_oracle
createuser --interactive cmo_user

# Set password for database user
psql -c "ALTER USER cmo_user PASSWORD 'your_secure_password_here';"

# Grant privileges
psql -c "GRANT ALL PRIVILEGES ON DATABASE clinical_metabolomics_oracle TO cmo_user;"

# Exit postgres user
exit
```

### Step 2: Configure PostgreSQL Security

```bash
# Edit PostgreSQL configuration
sudo nano /etc/postgresql/16/main/postgresql.conf

# Find and modify:
# listen_addresses = 'localhost'
# port = 5432

# Edit authentication configuration
sudo nano /etc/postgresql/16/main/pg_hba.conf

# Add line for local connections:
# local   clinical_metabolomics_oracle   cmo_user   md5

# Restart PostgreSQL
sudo systemctl restart postgresql
```

### Step 3: Test Database Connection

```bash
# Test connection
psql -h localhost -U cmo_user -d clinical_metabolomics_oracle

# If successful, you should see the PostgreSQL prompt
# \q to quit
```

## Application Deployment

### Step 1: Clone Repository

```bash
# Switch to application user
sudo su - cmo

# Clone the repository
cd /opt/cmo
git clone https://github.com/mberjans/cmo_fastapi_react.git
cd cmo_fastapi_react

# Set proper permissions
sudo chown -R cmo:cmo /opt/cmo/cmo_fastapi_react
```

### Step 2: Setup Backend (FastAPI)

```bash
# Navigate to backend directory
cd /opt/cmo/cmo_fastapi_react

# Create Python virtual environment
python3 -m venv venv

# Activate virtual environment
source venv/bin/activate

# Install Python dependencies
pip install -r requirements.txt

# Install additional production dependencies
pip install gunicorn uvicorn[standard] psycopg2-binary redis
```

### Step 3: Setup Frontend (React)

```bash
# Navigate to frontend directory
cd /opt/cmo/cmo_fastapi_react/cmo_frontend

# Install Node.js dependencies
npm install

# Build production version
npm run build

# Verify build
ls -la dist/  # Should contain built files
```

### Step 4: Environment Configuration

```bash
# Create environment file for backend
cd /opt/cmo/cmo_fastapi_react
cp .env.example .env

# Edit environment variables
nano .env
```

Add the following configuration to `.env`:

```bash
# Database Configuration
DATABASE_URL=postgresql://cmo_user:your_secure_password_here@localhost:5432/clinical_metabolomics_oracle

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Security
SECRET_KEY=your_very_long_random_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Keys (add your actual API keys)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Application Settings
ENVIRONMENT=production
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com,YOUR_SERVER_IP

# CORS Settings
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com

# Email Configuration (if using email features)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
```

### Step 5: Database Migration

```bash
# Activate virtual environment
source venv/bin/activate

# Run database migrations
python -m alembic upgrade head

# Create initial admin user (if applicable)
python scripts/create_admin_user.py
```

## Web Server Configuration

### Step 1: Configure Nginx

```bash
# Create Nginx configuration file
sudo nano /etc/nginx/sites-available/clinical-metabolomics-oracle
```

Add the following configuration:

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com YOUR_SERVER_IP;

    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    # SSL Configuration (will be added later)
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Serve React frontend
    location / {
        root /opt/cmo/cmo_fastapi_react/cmo_frontend/dist;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # Proxy API requests to FastAPI backend
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://127.0.0.1:8000/health;
        access_log off;
    }

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Client max body size
    client_max_body_size 10M;

    # Logging
    access_log /var/log/nginx/cmo_access.log;
    error_log /var/log/nginx/cmo_error.log;
}
```

### Step 2: Enable Nginx Site

```bash
# Enable the site
sudo ln -s /etc/nginx/sites-available/clinical-metabolomics-oracle /etc/nginx/sites-enabled/

# Remove default site
sudo rm /etc/nginx/sites-enabled/default

# Test Nginx configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx
```

## SSL Certificate Setup

### Step 1: Install Certbot

```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Follow the prompts to configure SSL
```

### Step 2: Auto-renewal Setup

```bash
# Test auto-renewal
sudo certbot renew --dry-run

# Check crontab for auto-renewal (should be automatically added)
sudo crontab -l
```

## Service Management

### Step 1: Create Systemd Service for Backend

```bash
# Create service file
sudo nano /etc/systemd/system/cmo-backend.service
```

Add the following configuration:

```ini
[Unit]
Description=Clinical Metabolomics Oracle Backend
After=network.target postgresql.service redis.service
Wants=postgresql.service redis.service

[Service]
Type=exec
User=cmo
Group=cmo
WorkingDirectory=/opt/cmo/cmo_fastapi_react
Environment=PATH=/opt/cmo/cmo_fastapi_react/venv/bin
ExecStart=/opt/cmo/cmo_fastapi_react/venv/bin/gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 127.0.0.1:8000
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=cmo-backend

[Install]
WantedBy=multi-user.target
```

### Step 2: Enable and Start Services

```bash
# Reload systemd
sudo systemctl daemon-reload

# Enable and start the backend service
sudo systemctl enable cmo-backend
sudo systemctl start cmo-backend

# Check service status
sudo systemctl status cmo-backend

# View logs
sudo journalctl -u cmo-backend -f
```

### Step 3: Create Process Management with PM2 (Alternative)

```bash
# Switch to application user
sudo su - cmo

# Navigate to project directory
cd /opt/cmo/cmo_fastapi_react

# Activate virtual environment
source venv/bin/activate

# Create PM2 ecosystem file
nano ecosystem.config.js
```

Add the following PM2 configuration:

```javascript
module.exports = {
  apps: [{
    name: 'cmo-backend',
    script: 'venv/bin/gunicorn',
    args: 'app.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 127.0.0.1:8000',
    cwd: '/opt/cmo/cmo_fastapi_react',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production'
    },
    error_file: '/opt/cmo/logs/backend-error.log',
    out_file: '/opt/cmo/logs/backend-out.log',
    log_file: '/opt/cmo/logs/backend-combined.log',
    time: true
  }]
};
```

```bash
# Create logs directory
mkdir -p /opt/cmo/logs

# Start application with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
pm2 startup
# Follow the instructions provided by PM2
```

## Monitoring and Logging

### Step 1: Setup Log Rotation

```bash
# Create logrotate configuration
sudo nano /etc/logrotate.d/cmo
```

Add the following configuration:

```
/var/log/nginx/cmo_*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx
    endscript
}

/opt/cmo/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 cmo cmo
    postrotate
        pm2 reload all
    endscript
}
```

### Step 2: Setup System Monitoring

```bash
# Install htop for system monitoring
sudo apt install -y htop

# Install fail2ban for security
sudo apt install -y fail2ban

# Configure fail2ban
sudo nano /etc/fail2ban/jail.local
```

Add the following fail2ban configuration:

```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true
port = ssh
logpath = /var/log/auth.log

[nginx-http-auth]
enabled = true
port = http,https
logpath = /var/log/nginx/cmo_error.log

[nginx-limit-req]
enabled = true
port = http,https
logpath = /var/log/nginx/cmo_error.log
maxretry = 10
```

```bash
# Start and enable fail2ban
sudo systemctl start fail2ban
sudo systemctl enable fail2ban
```

## Backup Strategy

### Step 1: Database Backup Script

```bash
# Create backup directory
sudo mkdir -p /opt/backups
sudo chown cmo:cmo /opt/backups

# Create backup script
nano /opt/cmo/scripts/backup_database.sh
```

Add the following backup script:

```bash
#!/bin/bash

# Database backup script
BACKUP_DIR="/opt/backups"
DB_NAME="clinical_metabolomics_oracle"
DB_USER="cmo_user"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/db_backup_$DATE.sql"

# Create backup
pg_dump -h localhost -U $DB_USER -d $DB_NAME > $BACKUP_FILE

# Compress backup
gzip $BACKUP_FILE

# Remove backups older than 30 days
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +30 -delete

echo "Database backup completed: $BACKUP_FILE.gz"
```

```bash
# Make script executable
chmod +x /opt/cmo/scripts/backup_database.sh

# Add to crontab for daily backups
crontab -e

# Add this line for daily backup at 2 AM
0 2 * * * /opt/cmo/scripts/backup_database.sh
```

### Step 2: Application Backup Script

```bash
# Create application backup script
nano /opt/cmo/scripts/backup_application.sh
```

Add the following script:

```bash
#!/bin/bash

# Application backup script
BACKUP_DIR="/opt/backups"
APP_DIR="/opt/cmo/cmo_fastapi_react"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/app_backup_$DATE.tar.gz"

# Create application backup (excluding node_modules and venv)
tar -czf $BACKUP_FILE -C /opt/cmo \
    --exclude='cmo_fastapi_react/node_modules' \
    --exclude='cmo_fastapi_react/venv' \
    --exclude='cmo_fastapi_react/.git' \
    --exclude='cmo_fastapi_react/cmo_frontend/dist' \
    cmo_fastapi_react

# Remove backups older than 7 days
find $BACKUP_DIR -name "app_backup_*.tar.gz" -mtime +7 -delete

echo "Application backup completed: $BACKUP_FILE"
```

```bash
# Make script executable
chmod +x /opt/cmo/scripts/backup_application.sh

# Add to crontab for weekly backups
crontab -e

# Add this line for weekly backup on Sunday at 3 AM
0 3 * * 0 /opt/cmo/scripts/backup_application.sh
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Service Won't Start

```bash
# Check service status
sudo systemctl status cmo-backend

# View detailed logs
sudo journalctl -u cmo-backend -n 50

# Check if port is in use
sudo netstat -tlnp | grep :8000

# Restart service
sudo systemctl restart cmo-backend
```

#### 2. Database Connection Issues

```bash
# Test database connection
psql -h localhost -U cmo_user -d clinical_metabolomics_oracle

# Check PostgreSQL status
sudo systemctl status postgresql

# View PostgreSQL logs
sudo tail -f /var/log/postgresql/postgresql-16-main.log
```

#### 3. Nginx Configuration Issues

```bash
# Test Nginx configuration
sudo nginx -t

# Check Nginx status
sudo systemctl status nginx

# View Nginx error logs
sudo tail -f /var/log/nginx/cmo_error.log
```

#### 4. SSL Certificate Issues

```bash
# Check certificate status
sudo certbot certificates

# Renew certificate manually
sudo certbot renew

# Test SSL configuration
openssl s_client -connect your-domain.com:443
```

#### 5. Performance Issues

```bash
# Monitor system resources
htop

# Check disk usage
df -h

# Monitor application logs
sudo journalctl -u cmo-backend -f

# Check database performance
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"
```

### Useful Commands

```bash
# Restart all services
sudo systemctl restart nginx postgresql redis-server cmo-backend

# View all logs
sudo journalctl -f

# Check open ports
sudo netstat -tlnp

# Monitor disk space
watch df -h

# Check memory usage
free -h

# View running processes
ps aux | grep -E "(nginx|postgres|redis|gunicorn)"
```

## Security Checklist

- [ ] Firewall configured properly
- [ ] SSH key authentication enabled
- [ ] Regular security updates scheduled
- [ ] SSL certificate installed and auto-renewal configured
- [ ] Database access restricted to localhost
- [ ] Strong passwords used for all accounts
- [ ] Fail2ban configured and running
- [ ] Regular backups scheduled
- [ ] Log monitoring in place
- [ ] Security headers configured in Nginx

## Maintenance Schedule

**Daily:**
- Monitor system logs
- Check service status
- Verify backup completion

**Weekly:**
- Review security logs
- Check disk space usage
- Update application if needed

**Monthly:**
- Update system packages
- Review and rotate logs
- Test backup restoration
- Security audit

## Additional Configuration

### Step 1: Environment Variables for Production

Create a comprehensive environment configuration:

```bash
# Create production environment file
sudo nano /opt/cmo/cmo_fastapi_react/.env.production
```

```bash
# Production Environment Configuration
NODE_ENV=production
ENVIRONMENT=production
DEBUG=False

# Database
DATABASE_URL=postgresql://cmo_user:your_secure_password@localhost:5432/clinical_metabolomics_oracle
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis
REDIS_URL=redis://localhost:6379/0
REDIS_POOL_SIZE=10

# Security
SECRET_KEY=your_256_bit_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS
CORS_ORIGINS=["https://your-domain.com", "https://www.your-domain.com"]
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# File Upload
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=/opt/cmo/uploads

# Logging
LOG_LEVEL=INFO
LOG_FILE=/opt/cmo/logs/application.log

# Email (if using)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USE_TLS=true
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password

# API Keys
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
```

### Step 2: Health Check Endpoint

Create a health check script:

```bash
# Create health check script
nano /opt/cmo/scripts/health_check.sh
```

```bash
#!/bin/bash

# Health check script
HEALTH_URL="http://localhost:8000/health"
LOG_FILE="/opt/cmo/logs/health_check.log"

# Function to log with timestamp
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> $LOG_FILE
}

# Check if service is responding
if curl -f -s $HEALTH_URL > /dev/null; then
    log_message "Health check PASSED"
    exit 0
else
    log_message "Health check FAILED - Service not responding"

    # Restart service if health check fails
    sudo systemctl restart cmo-backend
    log_message "Service restarted due to health check failure"

    # Send notification (optional)
    # echo "CMO service restarted on $(hostname)" | mail -s "Service Alert" <EMAIL>

    exit 1
fi
```

```bash
# Make executable
chmod +x /opt/cmo/scripts/health_check.sh

# Add to crontab for every 5 minutes
crontab -e
# Add: */5 * * * * /opt/cmo/scripts/health_check.sh
```

### Step 3: Performance Optimization

```bash
# Optimize PostgreSQL for production
sudo nano /etc/postgresql/16/main/postgresql.conf
```

Add these optimizations:

```
# Memory Settings
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# Checkpoint Settings
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# Connection Settings
max_connections = 100
```

```bash
# Restart PostgreSQL
sudo systemctl restart postgresql
```

### Step 4: Monitoring Dashboard Setup

```bash
# Install monitoring tools
sudo apt install -y prometheus node-exporter grafana

# Configure Prometheus
sudo nano /etc/prometheus/prometheus.yml
```

```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']

  - job_name: 'cmo-backend'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
```

## Deployment Automation

### Step 1: Deployment Script

```bash
# Create deployment script
nano /opt/cmo/scripts/deploy.sh
```

```bash
#!/bin/bash

# Deployment script for Clinical Metabolomics Oracle
set -e

APP_DIR="/opt/cmo/cmo_fastapi_react"
BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)

echo "Starting deployment at $(date)"

# Create backup before deployment
echo "Creating backup..."
/opt/cmo/scripts/backup_application.sh
/opt/cmo/scripts/backup_database.sh

# Navigate to application directory
cd $APP_DIR

# Pull latest changes
echo "Pulling latest changes..."
git pull origin main

# Backend deployment
echo "Updating backend..."
source venv/bin/activate
pip install -r requirements.txt

# Frontend deployment
echo "Building frontend..."
cd cmo_frontend
npm install
npm run build
cd ..

# Database migrations
echo "Running database migrations..."
source venv/bin/activate
python -m alembic upgrade head

# Restart services
echo "Restarting services..."
sudo systemctl restart cmo-backend
sudo systemctl reload nginx

# Health check
echo "Performing health check..."
sleep 10
if curl -f -s http://localhost:8000/health > /dev/null; then
    echo "Deployment successful!"
else
    echo "Deployment failed - health check failed"
    exit 1
fi

echo "Deployment completed at $(date)"
```

```bash
# Make executable
chmod +x /opt/cmo/scripts/deploy.sh
```

### Step 2: Rollback Script

```bash
# Create rollback script
nano /opt/cmo/scripts/rollback.sh
```

```bash
#!/bin/bash

# Rollback script
set -e

APP_DIR="/opt/cmo/cmo_fastapi_react"
BACKUP_DIR="/opt/backups"

echo "Starting rollback at $(date)"

# Get latest backup
LATEST_BACKUP=$(ls -t $BACKUP_DIR/app_backup_*.tar.gz | head -n1)

if [ -z "$LATEST_BACKUP" ]; then
    echo "No backup found for rollback"
    exit 1
fi

echo "Rolling back to: $LATEST_BACKUP"

# Stop services
sudo systemctl stop cmo-backend

# Restore application
cd /opt/cmo
tar -xzf $LATEST_BACKUP

# Restart services
sudo systemctl start cmo-backend
sudo systemctl reload nginx

echo "Rollback completed at $(date)"
```

```bash
# Make executable
chmod +x /opt/cmo/scripts/rollback.sh
```

## Final Verification

### Step 1: Complete System Test

```bash
# Test all services
sudo systemctl status nginx postgresql redis-server cmo-backend

# Test application endpoints
curl -I https://your-domain.com
curl -I https://your-domain.com/api/health

# Test database connection
psql -h localhost -U cmo_user -d clinical_metabolomics_oracle -c "SELECT version();"

# Test SSL certificate
openssl s_client -connect your-domain.com:443 -servername your-domain.com
```

### Step 2: Load Testing (Optional)

```bash
# Install Apache Bench for load testing
sudo apt install -y apache2-utils

# Test frontend
ab -n 100 -c 10 https://your-domain.com/

# Test API endpoint
ab -n 100 -c 10 https://your-domain.com/api/health
```

## Documentation and Handover

### Step 1: Create Operations Manual

```bash
# Create operations documentation
nano /opt/cmo/docs/operations.md
```

Include:
- Service restart procedures
- Backup and restore procedures
- Monitoring and alerting setup
- Troubleshooting guide
- Emergency contacts

### Step 2: Create Maintenance Schedule

```bash
# Create maintenance checklist
nano /opt/cmo/docs/maintenance_checklist.md
```

Include:
- Daily checks
- Weekly maintenance
- Monthly reviews
- Quarterly updates

This completes the comprehensive deployment guide for the Clinical Metabolomics Oracle on Google Cloud Platform with Ubuntu 24.04. The application should now be fully deployed, secured, and ready for production use.
