#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Base template for translation files (in English - to be translated)
const baseTranslation = {
  "common": {
    "loading": "Loading...",
    "error": "Error",
    "retry": "Retry",
    "save": "Save",
    "cancel": "Cancel",
    "delete": "Delete",
    "edit": "Edit",
    "close": "Close",
    "submit": "Submit",
    "send": "Send",
    "back": "Back",
    "next": "Next",
    "previous": "Previous",
    "search": "Search",
    "clear": "Clear",
    "refresh": "Refresh"
  },
  "navigation": {
    "chat": "Chat",
    "history": "Chat History",
    "account": "Account",
    "admin": "Admin",
    "logout": "Logout",
    "login": "Login",
    "register": "Register"
  },
  "auth": {
    "login": {
      "title": "Sign In",
      "subtitle": "Access your Clinical Metabolomics Oracle account",
      "email": "Email Address",
      "password": "Password",
      "submit": "Sign In",
      "sending": "Signing in...",
      "noAccount": "Don't have an account? <link>Sign up here</link>",
      "signUp": "Sign up here",
      "invalidCredentials": "Invalid credentials"
    },
    "register": {
      "title": "Create Account",
      "subtitle": "Join the Clinical Metabolomics Oracle",
      "firstName": "First Name",
      "lastName": "Last Name",
      "email": "Email Address",
      "password": "Password",
      "confirmPassword": "Confirm Password",
      "submit": "Create Account",
      "registering": "Registering...",
      "hasAccount": "Already have an account? <link>Sign in here</link>",
      "signIn": "Sign in here",
      "passwordMismatch": "Passwords do not match",
      "passwordTooShort": "Password must be at least 8 characters long",
      "registrationSuccess": "Registration successful! Please log in.",
      "registrationFailed": "Registration failed. Please try again."
    },
    "account": {
      "title": "Account Settings",
      "subtitle": "Manage your profile and preferences",
      "profile": "Profile Information",
      "language": "Language Preference",
      "updateSuccess": "Profile updated successfully",
      "updateError": "Failed to update profile"
    }
  },
  "chat": {
    "title": "Clinical Metabolomics Oracle",
    "subtitle": "Your AI assistant for metabolomics analysis",
    "placeholder": "Ask a question about metabolomics...",
    "send": "Send",
    "newChat": "New Chat",
    "thinking": "Thinking...",
    "error": "Error sending message",
    "retry": "Retry",
    "welcome": {
      "title": "Welcome to Clinical Metabolomics Oracle",
      "subtitle": "How can I help you with your metabolomics research today?",
      "examples": [
        "Explain mass spectrometry analysis to me",
        "What are the best practices for sample preparation?",
        "How do I interpret metabolic pathways?"
      ]
    }
  },
  "history": {
    "title": "Chat History",
    "subtitle": "View your previous conversations",
    "empty": {
      "title": "No chat history yet",
      "subtitle": "Start a new conversation to see it appear here.",
      "action": "Start New Chat"
    },
    "loadError": "Error loading chat history",
    "clickToContinue": "Click to continue conversation",
    "today": "Today",
    "yesterday": "Yesterday",
    "daysAgo": "{{count}} days ago"
  },
  "language": {
    "english": "English",
    "spanish": "Español",
    "french": "Français",
    "german": "Deutsch",
    "italian": "Italiano",
    "portuguese": "Português",
    "russian": "Русский",
    "chinese": "中文",
    "japanese": "日本語",
    "korean": "한국어",
    "arabic": "العربية",
    "hindi": "हिन्दी",
    "dutch": "Nederlands",
    "swedish": "Svenska",
    "norwegian": "Norsk",
    "danish": "Dansk",
    "finnish": "Suomi",
    "polish": "Polski",
    "turkish": "Türkçe",
    "thai": "ไทย",
    "vietnamese": "Tiếng Việt",
    "indonesian": "Bahasa Indonesia",
    "malay": "Bahasa Melayu",
    "hebrew": "עברית",
    "czech": "Čeština",
    "hungarian": "Magyar",
    "romanian": "Română",
    "bulgarian": "Български",
    "croatian": "Hrvatski",
    "slovak": "Slovenčina",
    "slovenian": "Slovenščina",
    "estonian": "Eesti",
    "latvian": "Latviešu",
    "lithuanian": "Lietuvių",
    "ukrainian": "Українська",
    "greek": "Ελληνικά",
    "switchLanguage": "Switch Language"
  },
  "errors": {
    "generic": "An error occurred. Please try again.",
    "network": "Network error. Please check your connection.",
    "unauthorized": "You are not authorized to perform this action.",
    "notFound": "The requested resource was not found.",
    "serverError": "Server error. Please try again later."
  }
};

// Languages that need translation files (excluding ones we already created)
const languagesToCreate = [
  'it', 'pt', 'ru', 'ja', 'ko', 'ar', 'hi', 'nl', 'sv', 'no', 'da', 'fi', 
  'pl', 'tr', 'th', 'vi', 'id', 'ms', 'he', 'cs', 'hu', 'ro', 'bg', 'hr', 
  'sk', 'sl', 'et', 'lv', 'lt', 'uk', 'el'
];

const localesDir = path.join(__dirname, '..', 'public', 'locales');

// Create directories and translation files
languagesToCreate.forEach(lang => {
  const langDir = path.join(localesDir, lang);
  const translationFile = path.join(langDir, 'translation.json');
  
  // Create directory if it doesn't exist
  if (!fs.existsSync(langDir)) {
    fs.mkdirSync(langDir, { recursive: true });
  }
  
  // Create translation file with base template
  fs.writeFileSync(translationFile, JSON.stringify(baseTranslation, null, 2));
  console.log(`Created translation file for ${lang}`);
});

console.log('All translation files created successfully!');
console.log('Note: These files contain English text and should be translated to the respective languages.');
