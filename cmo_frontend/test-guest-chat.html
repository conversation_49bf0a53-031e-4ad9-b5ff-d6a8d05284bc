<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Guest Chat</title>
</head>
<body>
    <h1>Test Guest Chat API</h1>
    <div>
        <input type="text" id="messageInput" placeholder="Enter your message" />
        <button onclick="sendMessage()">Send</button>
    </div>
    <div id="response" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc; min-height: 100px;"></div>

    <script>
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const responseDiv = document.getElementById('response');
            const message = input.value.trim();
            
            if (!message) return;
            
            responseDiv.innerHTML = 'Sending message...';
            
            try {
                const apiBaseUrl = 'http://localhost:8000/api/v1';
                const response = await fetch(`${apiBaseUrl}/chat/guest`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message }),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                if (!response.body) {
                    throw new Error('No response body');
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let aiResponse = '';

                responseDiv.innerHTML = `<strong>User:</strong> ${message}<br><strong>AI:</strong> `;

                while (true) {
                    const { done, value } = await reader.read();

                    if (done) {
                        break;
                    }

                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));

                                if (data.type === 'token') {
                                    aiResponse += data.content;
                                    responseDiv.innerHTML = `<strong>User:</strong> ${message}<br><strong>AI:</strong> ${aiResponse}`;
                                } else if (data.type === 'status') {
                                    responseDiv.innerHTML = `<strong>User:</strong> ${message}<br><strong>Status:</strong> ${data.message}`;
                                } else if (data.type === 'complete') {
                                    responseDiv.innerHTML = `<strong>User:</strong> ${message}<br><strong>AI:</strong> ${aiResponse}<br><strong>Status:</strong> Complete`;
                                    return;
                                } else if (data.type === 'error') {
                                    responseDiv.innerHTML = `<strong>Error:</strong> ${data.message}`;
                                    return;
                                }
                            } catch (parseError) {
                                console.error('Error parsing SSE data:', parseError);
                            }
                        }
                    }
                }
            } catch (error) {
                console.error('Fetch error:', error);
                responseDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
            }
        }

        // Allow Enter key to send message
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
