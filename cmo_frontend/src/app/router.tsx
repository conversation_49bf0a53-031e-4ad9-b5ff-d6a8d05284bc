import { createBrowserRouter, Navigate } from 'react-router-dom'
import ProtectedRouteLayout from '@/components/ProtectedRouteLayout'
import AdminRouteLayout from '@/components/AdminRouteLayout'
import RegistrationPage from '@/pages/RegistrationPage'
import LoginPage from '@/pages/LoginPage'
import AccountPage from '@/pages/AccountPage'
import ChatPage from '@/pages/ChatPage'
import ChatHistoryPage from '@/pages/ChatHistoryPage'
import GuestChatPage from '@/pages/GuestChatPage'

// Placeholder components - these will be created in later tickets
const AdminDashboard = () => <div>Admin Dashboard - Coming Soon</div>

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Navigate to="/chat" replace />,
  },
  {
    path: '/login',
    element: <LoginPage />,
  },
  {
    path: '/register',
    element: <RegistrationPage />,
  },
  {
    path: '/guest-chat',
    element: <GuestChatPage />,
  },
  {
    path: '/',
    element: <ProtectedRouteLayout />,
    children: [
      {
        path: '/chat',
        element: <ChatPage />,
      },
      {
        path: '/history',
        element: <ChatHistoryPage />,
      },
      {
        path: '/account',
        element: <AccountPage />,
      },
      {
        path: '/admin',
        element: <AdminRouteLayout />,
        children: [
          {
            path: '',
            element: <AdminDashboard />,
          },
        ],
      },
    ],
  },
  {
    path: '*',
    element: <Navigate to="/chat" replace />,
  },
])

export default router
