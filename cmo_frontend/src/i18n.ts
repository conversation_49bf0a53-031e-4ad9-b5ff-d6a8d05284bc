import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import HttpBackend from 'i18next-http-backend'
import LanguageDetector from 'i18next-browser-languagedetector'

const initOptions = {
    // Default language
    fallbackLng: 'en',
    
    // Supported languages
    supportedLngs: [
      'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'zh', 'ja', 'ko',
      'ar', 'hi', 'nl', 'sv', 'no', 'da', 'fi', 'pl', 'tr', 'th',
      'vi', 'id', 'ms', 'he', 'cs', 'hu', 'ro', 'bg', 'hr', 'sk',
      'sl', 'et', 'lv', 'lt', 'uk', 'el'
    ],
    
    // Debug mode (set to false in production)
    debug: import.meta.env.DEV,
    
    // Language detection options
    detection: {
      // Order of language detection methods
      order: [
        'localStorage',     // Check localStorage first
        'navigator',        // Then browser language
        'htmlTag',          // Then HTML lang attribute
        'path',             // Then URL path
        'subdomain'         // Finally subdomain
      ],
      
      // Cache user language preference
      caches: ['localStorage'],
      
      // Don't lookup language from path or subdomain by default
      lookupFromPathIndex: 0,
      lookupFromSubdomainIndex: 0,
      
      // Exclude certain paths from language detection
      excludeCacheFor: ['cimode'],
      
      // Check for language in these HTML attributes
      htmlTag: document.documentElement,
      
      // Convert language codes (e.g., en-US -> en)
      convertDetectedLanguage: (lng: string): string => {
        // Extract the primary language code
        return lng.split('-')[0]
      }
    },
    
    // Backend options for loading translations
    backend: {
      // Path to load resources from
      loadPath: '/locales/{{lng}}/{{ns}}.json',
      
      // Allow cross domain requests
      crossDomain: false,
      
      // Request timeout
      requestOptions: {
        cache: 'default'
      }
    },
    
    // Interpolation options
    interpolation: {
      // React already escapes values to prevent XSS
      escapeValue: false,
      
      // Format function for custom formatting
      format: (value: any, format?: string) => {
        if (format === 'uppercase') return value.toUpperCase()
        if (format === 'lowercase') return value.toLowerCase()
        if (format === 'capitalize') {
          return value.charAt(0).toUpperCase() + value.slice(1)
        }
        return value
      }
    },
    
    // React options
    react: {
      // Use Suspense for async loading
      useSuspense: true,

      // Bind i18n instance to component
      bindI18n: 'languageChanged',

      // Bind i18n store to component
      bindI18nStore: '',

      // How to handle missing translations
      transEmptyNodeValue: '',

      // Transform the node if translation is missing
      transSupportBasicHtmlNodes: true,
      transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'em', 'span']
    },
    
    // Namespace options
    ns: ['translation'],
    defaultNS: 'translation',
    
    // Key separator for nested translations
    keySeparator: '.',
    
    // Namespace separator
    nsSeparator: ':',
    
    // Pluralization
    pluralSeparator: '_',
    contextSeparator: '_',
    
    // Missing key handling
    saveMissing: import.meta.env.DEV,
    missingKeyHandler: import.meta.env.DEV ? (lngs: readonly string[], _ns: string, key: string, _fallbackValue: string, _updateMissing: boolean, _options: any) => {
      console.warn(`Missing translation key: ${key} for languages: ${lngs.join(', ')}`)
    } : undefined,
    
    // Return objects for missing translations in development
    returnObjects: false,
    returnEmptyString: false,
    returnNull: false,
    
    // Join arrays
    joinArrays: '',

    // Post processing
    postProcess: [],
    
    // Clean code on production
    cleanCode: true,
    
    // Compatibility with v1
    compatibilityJSON: 'v4' as const
}

i18n
  // Load translation using http backend
  .use(HttpBackend)
  // Detect user language
  .use(LanguageDetector)
  // Pass the i18n instance to react-i18next
  .use(initReactI18next)
  // Initialize i18next
  .init(initOptions)

export default i18n
