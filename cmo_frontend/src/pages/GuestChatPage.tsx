import { useState, useEffect, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { Link } from 'react-router-dom'
import LanguageSwitcher from '@/components/LanguageSwitcher'

interface ChatMessage {
  content: string
  sender: 'user' | 'ai'
  timestamp: string
}

const GuestChatPage = () => {
  const { t } = useTranslation()
  const [inputMessage, setInputMessage] = useState('')
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [status, setStatus] = useState<'idle' | 'loading' | 'error'>('idle')
  const [currentStatus, setCurrentStatus] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleStreamingResponse = async (messageContent: string) => {
    try {
      setStatus('loading')
      setError(null)

      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1'
      const response = await fetch(`${apiBaseUrl}/chat/guest`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message: messageContent }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      if (!response.body) {
        throw new Error('No response body')
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()

      // Add AI message placeholder
      const aiMessage: ChatMessage = {
        content: '',
        sender: 'ai',
        timestamp: new Date().toISOString(),
      }
      setMessages(prev => [...prev, aiMessage])

      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          setStatus('idle')
          setCurrentStatus(null)
          break
        }

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))

              if (data.type === 'token') {
                setMessages(prev => {
                  const newMessages = [...prev]
                  const lastMessage = newMessages[newMessages.length - 1]
                  if (lastMessage && lastMessage.sender === 'ai') {
                    lastMessage.content += data.content
                  }
                  return newMessages
                })
              } else if (data.type === 'status') {
                setCurrentStatus(data.message)
              } else if (data.type === 'complete') {
                setStatus('idle')
                setCurrentStatus(null)
                return
              } else if (data.type === 'error') {
                setError(data.message)
                setStatus('error')
                return
              }
            } catch (parseError) {
              console.error('Error parsing SSE data:', parseError)
            }
          }
        }
      }
    } catch (error) {
      console.error('Streaming error:', error)
      setError(error instanceof Error ? error.message : t('errors.generic'))
      setStatus('error')
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!inputMessage.trim() || status === 'loading') {
      return
    }

    const messageToSend = inputMessage.trim()
    setInputMessage('')

    // Add user message
    const userMessage: ChatMessage = {
      content: messageToSend,
      sender: 'user',
      timestamp: new Date().toISOString(),
    }
    setMessages(prev => [...prev, userMessage])

    // Handle streaming response
    await handleStreamingResponse(messageToSend)
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  return (
    <div style={{
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: '#f5f5f5'
    }}>
      {/* Header */}
      <div style={{
        backgroundColor: 'white',
        padding: '1rem 2rem',
        borderBottom: '1px solid #e0e0e0',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div>
          <h1 style={{ margin: 0, color: '#333' }}>
            {t('chat.guest.title')}
          </h1>
          <p style={{ margin: '0.5rem 0 0 0', color: '#666', fontSize: '0.9rem' }}>
            {t('chat.guest.subtitle')}
          </p>
        </div>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
          <LanguageSwitcher size="small" />
          <Link
            to="/login"
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              textDecoration: 'none',
              fontSize: '0.9rem'
            }}
          >
            {t('auth.login.title')}
          </Link>
          <Link
            to="/register"
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#f8f9fa',
              color: '#007bff',
              border: '1px solid #007bff',
              borderRadius: '6px',
              textDecoration: 'none',
              fontSize: '0.9rem'
            }}
          >
            {t('auth.register.title')}
          </Link>
        </div>
      </div>

      {/* Guest Notice */}
      <div style={{
        backgroundColor: '#fff3cd',
        color: '#856404',
        padding: '0.75rem 2rem',
        borderBottom: '1px solid #ffeaa7',
        fontSize: '0.9rem'
      }}>
        <strong>{t('chat.guest.notice.title')}:</strong> {t('chat.guest.notice.message')}
      </div>

      {/* Messages Area */}
      <div style={{
        flex: 1,
        overflowY: 'auto',
        padding: '1rem',
        display: 'flex',
        flexDirection: 'column',
        gap: '1rem'
      }}>
        {messages.length === 0 && (
          <div style={{
            textAlign: 'center',
            color: '#666',
            marginTop: '2rem'
          }}>
            <h3>{t('chat.guest.welcome.title')}</h3>
            <p>{t('chat.guest.welcome.subtitle')}</p>
          </div>
        )}

        {messages.map((message, index) => (
          <div
            key={index}
            style={{
              display: 'flex',
              justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start',
              marginBottom: '0.5rem'
            }}
          >
            <div style={{
              maxWidth: '70%',
              padding: '0.75rem 1rem',
              borderRadius: '18px',
              backgroundColor: message.sender === 'user' ? '#007bff' : 'white',
              color: message.sender === 'user' ? 'white' : '#333',
              boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
              border: message.sender === 'ai' ? '1px solid #e0e0e0' : 'none'
            }}>
              <div style={{ whiteSpace: 'pre-wrap', lineHeight: '1.4' }}>
                {message.content}
              </div>
              <div style={{
                fontSize: '0.75rem',
                opacity: 0.7,
                marginTop: '0.25rem',
                textAlign: message.sender === 'user' ? 'right' : 'left'
              }}>
                {formatTimestamp(message.timestamp)}
              </div>
            </div>
          </div>
        ))}

        {/* Status indicator */}
        {currentStatus && (
          <div style={{
            display: 'flex',
            justifyContent: 'flex-start',
            marginBottom: '0.5rem'
          }}>
            <div style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#f0f0f0',
              borderRadius: '18px',
              color: '#666',
              fontSize: '0.9rem',
              fontStyle: 'italic'
            }}>
              {currentStatus}
            </div>
          </div>
        )}

        {/* Loading indicator */}
        {status === 'loading' && !currentStatus && (
          <div style={{
            display: 'flex',
            justifyContent: 'flex-start',
            marginBottom: '0.5rem'
          }}>
            <div style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#f0f0f0',
              borderRadius: '18px',
              color: '#666',
              fontSize: '0.9rem'
            }}>
              <span>{t('chat.input.thinking')}</span>
              <span style={{ animation: 'pulse 1.5s infinite' }}>...</span>
            </div>
          </div>
        )}

        {/* Error display */}
        {error && (
          <div style={{
            backgroundColor: '#fee',
            color: '#c33',
            padding: '0.75rem',
            borderRadius: '8px',
            border: '1px solid #fcc'
          }}>
            {t('common.error')}: {error}
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div style={{
        backgroundColor: 'white',
        padding: '1rem 2rem',
        borderTop: '1px solid #e0e0e0'
      }}>
        <form onSubmit={handleSubmit} style={{ display: 'flex', gap: '0.5rem' }}>
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            placeholder={t('chat.input.placeholder')}
            disabled={status === 'loading'}
            style={{
              flex: 1,
              padding: '0.75rem 1rem',
              border: '1px solid #ddd',
              borderRadius: '25px',
              fontSize: '1rem',
              outline: 'none',
              backgroundColor: status === 'loading' ? '#f5f5f5' : 'white'
            }}
          />
          <button
            type="submit"
            disabled={!inputMessage.trim() || status === 'loading'}
            style={{
              padding: '0.75rem 1.5rem',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '25px',
              fontSize: '1rem',
              cursor: (!inputMessage.trim() || status === 'loading') ? 'not-allowed' : 'pointer',
              opacity: (!inputMessage.trim() || status === 'loading') ? 0.6 : 1,
              minWidth: '80px'
            }}
          >
            {status === 'loading' ? '...' : t('common.send')}
          </button>
        </form>
      </div>

      {/* Add CSS animation for loading dots */}
      <style>{`
        @keyframes pulse {
          0%, 50% { opacity: 1; }
          100% { opacity: 0.3; }
        }
      `}</style>
    </div>
  )
}

export default GuestChatPage
