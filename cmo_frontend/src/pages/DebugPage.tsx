import React from 'react'

const DebugPage = () => {
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1'
  
  const testFetch = async () => {
    try {
      console.log('Testing fetch to:', `${apiBaseUrl}/chat/guest`)
      const response = await fetch(`${apiBaseUrl}/chat/guest`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message: 'test' }),
      })
      
      console.log('Response status:', response.status)
      console.log('Response ok:', response.ok)
      console.log('Response headers:', response.headers)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      console.log('Fetch successful!')
    } catch (error) {
      console.error('Fetch error:', error)
    }
  }

  return (
    <div style={{ padding: '2rem' }}>
      <h1>Debug Page</h1>
      <div>
        <h3>Environment Variables:</h3>
        <p><strong>VITE_API_BASE_URL:</strong> {import.meta.env.VITE_API_BASE_URL || 'undefined'}</p>
        <p><strong>Computed API Base URL:</strong> {apiBaseUrl}</p>
        <p><strong>Mode:</strong> {import.meta.env.MODE}</p>
        <p><strong>Dev:</strong> {import.meta.env.DEV ? 'true' : 'false'}</p>
      </div>
      
      <div style={{ marginTop: '2rem' }}>
        <h3>Test API:</h3>
        <button onClick={testFetch} style={{ padding: '0.5rem 1rem', backgroundColor: '#007bff', color: 'white', border: 'none', borderRadius: '4px' }}>
          Test Fetch to Guest Chat API
        </button>
        <p style={{ fontSize: '0.9rem', color: '#666', marginTop: '0.5rem' }}>
          Check browser console for results
        </p>
      </div>
    </div>
  )
}

export default DebugPage
